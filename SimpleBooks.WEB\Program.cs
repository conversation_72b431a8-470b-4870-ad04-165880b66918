namespace SimpleBooks.WEB
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            //Authentication
            builder.Services.AddHttpContextAccessor();
            builder.Services.InjectWebAuthenticationServices();
            builder.Services.InjectServiceServices();

            // Add services to the container.
            builder.Services.AddControllersWithViews().AddNewtonsoftJson(options =>
            {
                options.SerializerSettings.PreserveReferencesHandling = PreserveReferencesHandling.All;
                options.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Serialize;
                options.SerializerSettings.ContractResolver = new DefaultContractResolver
                {
                    NamingStrategy = null
                };
            });
            builder.Services.AddDistributedMemoryCache();
            builder.Services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(30);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            builder.Services.AddControllersWithViews().AddRazorOptions(options =>
            {
                options.ViewLocationFormats.Add("/Views/Business/HR/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Purchases/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Sales/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Treasury/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/User/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Warehouse/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Warehouse/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Treasury/BankManagement/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Treasury/BankManagement/BankTransferManagement/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Treasury/BankManagement/CheckManagement/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Treasury/CashManagement/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/Treasury/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Business/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/Reports/{1}/{0}.cshtml");
                options.ViewLocationFormats.Add("/Views/{1}/{0}.cshtml");
            });
            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseSession();

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllerRoute(
                name: "default",
                pattern: "{controller=Home}/{action=Index}/{id?}");

            app.Run();
        }
    }
}
