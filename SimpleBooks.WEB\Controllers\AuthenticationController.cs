using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;

namespace SimpleBooks.Web.Controllers
{
    public class AuthenticationController : Controller
    {
        private readonly Services.Core.Authentication.IAuthenticationService _loginService;

        public AuthenticationController(Services.Core.Authentication.IAuthenticationService loginService)
        {
            _loginService = loginService;
        }

        [HttpGet]
        public IActionResult Login()
        {
            LoginRequest loginDto = new LoginRequest();
            return View(loginDto);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginRequest model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                var result = await _loginService.Login(model);

                result.ThrowIfFailure();

                if (result.Data is not null)
                {
                    var tokenHandler = new JwtSecurityTokenHandler();
                    var jwtToken = tokenHandler.ReadToken(result.Data.Token) as JwtSecurityToken;
                    if (jwtToken != null)
                    {
                        // Save identity in cookie
                        var identity = new ClaimsIdentity(jwtToken.Claims, CookieAuthenticationDefaults.AuthenticationScheme);
                        await HttpContext.SignInAsync(
                            CookieAuthenticationDefaults.AuthenticationScheme,
                            new ClaimsPrincipal(identity));
                        // Redirect to the desired page after login
                        return RedirectToAction("Index", "Home");
                    }
                }
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("login", ex.Message);
                return View(model);
            }
            return BadRequest();
        }

        [HttpGet]
        public IActionResult UnAuthorizedIndex()
        {
            return View();
        }
    }
}
